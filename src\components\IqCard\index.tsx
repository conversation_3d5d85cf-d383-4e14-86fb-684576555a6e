import Image from 'next/image';

interface IqCardProps {
  imgSource: string;
  name: string;
  text: string;
}

export default function IqCard({ imgSource, name, text }: IqCardProps) {
  return (
    <div className="bg-white rounded-t-lg w-[115px] md:w-[153px]">
      <div className="py-[6px] px-[4px]">
        <p className="text-center text-green font-semibold text-[12px] leading-[16px] md:text-base">{text}</p>
      </div>
      <div className={`rounded-t-lg ${name === 'You' ? 'bg-green' : 'bg-grey-93'}`}>
        <div className="pt-[10px] px-2">
          <p
            className={`text-center ${
              name === 'You' ? 'text-white' : 'text-black'
            } font-segoe font-semibold text-[12px] leading-[21px] md:text-base`}>
            {name}
          </p>
        </div>
        <div>
          <img
            src={`/images/checkout-v3/${imgSource}.png`}
            alt="..."
            className={`${name === 'Ice Spice' ? 'md:ml-[7px]' : 'mx-auto'} mx-auto max-w-full max-h-full grayscale`}
          />
        </div>
      </div>
    </div>
  );
}
