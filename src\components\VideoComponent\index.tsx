'use client';

import { useEffect, useRef, useState } from 'react';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

interface VideoComponentProps {
  source: {
    header: string;
    src: string;
    week: number;
  };
  week: number;
}

const STREAM_SDK_URL = 'https://embed.cloudflarestream.com/embed/sdk.latest.js';

export default function VideoComponent({ source, week }: VideoComponentProps) {
  const { captureEvent } = usePostHogAnalytics();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [hasPlayed, setHasPlayed] = useState(false);

  useEffect(() => {
    if (!iframeRef.current || hasPlayed) return;

    // Load the Cloudflare Stream SDK
    let script = document.querySelector<HTMLScriptElement>(`script[src="${STREAM_SDK_URL}"]`);
    if (!script) {
      script = document.createElement('script');
      script.src = STREAM_SDK_URL;
      document.head.appendChild(script);
    }

    script.onload = () => {
      if (window.Stream) {
        const player = window.Stream(iframeRef.current);

        // Listen to the play event
        player.addEventListener('play', () => {
          if (!hasPlayed && week) {
            if (week < 1 || week > 5) {
              console.warn(`Invalid week prop: ${week}. Expected 1-5.`);
              return;
            }
            const eventName = PostHogEventEnum[`PLAYED_WEEK_${week}_VIDEO` as keyof typeof PostHogEventEnum];
            if (eventName) {
              captureEvent(eventName, { title: source.header });
              setHasPlayed(true); // Mark as played to avoid multiple events
            }
          }
        });
      }
    };

    return () => {
      // Clean up the script when the component unmounts
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [source.src, hasPlayed, captureEvent, source.header, week]);

  // Reset hasPlayed when the source changes
  useEffect(() => {
    setHasPlayed(false);
  }, [source.src]);

  return (
    <div className=" md:w-[800px]" style={{ position: 'relative', paddingTop: '56.25%' }}>
      <iframe
        ref={iframeRef}
        src={source.src}
        loading="lazy"
        className="m-auto"
        style={{
          border: 'none',
          position: 'absolute',
          top: 0,
          left: 0,
          height: '100%',
          width: '100%',
        }}
        allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture"
        allowFullScreen></iframe>
    </div>
  );
}
