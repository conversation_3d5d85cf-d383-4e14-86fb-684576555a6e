import { useEffect, useState } from 'react';

interface LatestResultPartProps {
  image: string;
  name: string;
  iq: number;
  mobileColored: boolean;
  isFirst: boolean;
  twoMins?: boolean;
}

export default function LatestResultPart({
  image,
  name,
  iq,
  mobileColored,
  isFirst,
  twoMins = false,
}: LatestResultPartProps) {
  const [show, setShow] = useState(false);

  // Effect to handle the transition only for the first element
  useEffect(() => {
    if (isFirst) {
      setShow(false);
      const timer = setTimeout(() => setShow(true), 500); // Match duration of your transition
      return () => clearTimeout(timer);
    }
  }, [image, name, iq, isFirst]); // Dependencies to trigger effect on change

  return (
    <div
      className={`flex w-full justify-between p-5 font-raleway ${name == ' ' && 'hidden'} ${name == '' && 'hidden'} ${
        mobileColored ? 'bg-[#F6F9FF] md:bg-transparent rounded' : ''
      }`}>
      <div
        className={`${
          isFirst && !show ? 'opacity-0' : 'opacity-100'
        } transition-opacity duration-500 ease-in-out flex`}>
        <img src={image} className="transition-opacity duration-500 h-[34px] w-[62px] mt-1" alt={`${name}'s result`} />
        <div>
          <p
            className={`pl-4 font-bold text-[20px] text-[#191919] ${
              isFirst && !show ? 'opacity-0' : 'opacity-100'
            } transition-opacity duration-500`}>
            {name}
          </p>
          <p className="pl-4 text-left text-[#8893AC] text-[16px]"> {twoMins == true ? 'two mins ago' : 'a min ago'}</p>
        </div>
      </div>
      <div className={`${isFirst && !show ? 'opacity-0' : 'opacity-100'} transition-opacity duration-500`}>
        <p className="text-right text-[#8893AC] text-[16px]">IQ</p>
        <p
          className={`font-bold text-[20px] text-[#191919] ${
            isFirst && !show ? 'opacity-0' : 'opacity-100'
          } transition-opacity duration-500`}>
          {iq}
        </p>
      </div>
    </div>
  );
}
