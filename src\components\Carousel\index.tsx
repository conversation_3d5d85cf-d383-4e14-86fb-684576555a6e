'use client';

import dynamic from 'next/dynamic';
import React, { ReactNode, useEffect, useRef } from 'react';
import 'swiper/css';
import 'swiper/css/autoplay'; // Autoplay styles
import { Autoplay } from 'swiper/modules';
import type { SwiperRef } from 'swiper/react';

interface ImageSwiperProps {
  /** Pass your slides as children wrapped in `<SwiperSlide>` */
  children: ReactNode;
}

// Dynamically import Swiper (to avoid SSR issues)
const Swiper = dynamic(() => import('swiper/react').then(mod => mod.Swiper), {
  ssr: false,
});

function ImageSwiper({ children }: ImageSwiperProps) {
  const swiperRef = useRef(null);

  const handleMouseEnter = () => {
    swiperRef.current?.autoplay.stop();
  };

  const handleMouseLeave = () => {
    swiperRef.current?.autoplay.start();
  };

  useEffect(() => {
    if (swiperRef.current) {
      const swiperEl = swiperRef.current.el;

      // Pause autoplay on hover
      swiperEl.addEventListener('mouseenter', () => {
        if (swiperRef.current) {
          swiperRef.current.autoplay.stop();
        }
      });

      // Resume autoplay when unhovered
      swiperEl.addEventListener('mouseleave', () => {
        if (swiperRef.current) {
          swiperRef.current.autoplay.start();
        }
      });

      // Clean up event listeners
      return () => {
        swiperEl.removeEventListener('mouseenter', () => {
          swiperRef.current.autoplay.stop();
        });
        swiperEl.removeEventListener('mouseleave', () => {
          swiperRef.current.autoplay.start();
        });
      };
    }
  }, []);

  return (
    <div className="w-full h-64 overflow-hidden">
      <Swiper
        ref={swiperRef}
        slidesPerView={3}
        spaceBetween={30}
        loop={true}
        autoplay={{ delay: 2500, disableOnInteraction: false }}
        modules={[Autoplay]}
        className="swiper-container flex justify-center">
        {children}
      </Swiper>
    </div>
  );
}

export default ImageSwiper;
